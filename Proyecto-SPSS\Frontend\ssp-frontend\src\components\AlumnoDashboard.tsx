import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Avatar,
  Chip,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Person as PersonIcon,
  School as SchoolIcon,
  Group as GroupIcon,
  Psychology as PsychologyIcon,
  Edit as EditIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import type { Persona } from '../types/index';

interface AlumnoDashboardProps {
  user: Persona;
  onEditProfile: () => void;
}

const AlumnoDashboard = ({ user, onEditProfile }: AlumnoDashboardProps) => {
  const [loading, setLoading] = useState(false);

  // Función para obtener las iniciales del nombre
  const getInitials = (correo: string) => {
    const name = correo.split('@')[0];
    return name.substring(0, 2).toUpperCase();
  };

  // Función para formatear la información del usuario
  const formatUserInfo = () => {
    const info = [];
    
    if (user.edad) info.push(`${user.edad} años`);
    if (user.semestre) info.push(`Semestre ${user.semestre}`);
    if (user.matricula) info.push(`Matrícula: ${user.matricula}`);
    
    return info.join(' • ');
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header del Dashboard */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Mi Perfil Estudiantil
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Bienvenido a tu espacio personal en el Sistema de Seguimiento Psicopedagógico
        </Typography>
      </Box>

      <Grid container spacing={3}>
        
        {/* Tarjeta de Perfil Principal */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <Avatar
                sx={{ 
                  width: 80, 
                  height: 80, 
                  mx: 'auto', 
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '1.5rem'
                }}
              >
                {getInitials(user.correo_institucional)}
              </Avatar>
              
              <Typography variant="h6" gutterBottom>
                {user.correo_institucional.split('@')[0]}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {user.correo_institucional}
              </Typography>
              
              <Typography variant="body2" sx={{ mb: 2 }}>
                {formatUserInfo()}
              </Typography>
              
              <Chip 
                label="Estudiante" 
                color="primary" 
                variant="outlined"
                sx={{ mb: 2 }}
              />
              
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<EditIcon />}
                  onClick={onEditProfile}
                  fullWidth
                >
                  Editar Perfil
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Información Académica */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <SchoolIcon sx={{ mr: 1 }} />
                Información Académica
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Matrícula
                    </Typography>
                    <Typography variant="body1">
                      {user.matricula || 'No asignada'}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Semestre Actual
                    </Typography>
                    <Typography variant="body1">
                      {user.semestre ? `${user.semestre}° Semestre` : 'No especificado'}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Programa Educativo
                    </Typography>
                    <Typography variant="body1">
                      {user.programas && user.programas.length > 0 
                        ? user.programas[0].nombre_programa 
                        : 'Pendiente de asignación'}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Grupo
                    </Typography>
                    <Typography variant="body1">
                      {user.grupos && user.grupos.length > 0 
                        ? user.grupos[0].nombre_grupo 
                        : 'Pendiente de asignación'}
                    </Typography>
                  </Paper>
                </Grid>

                {user.cohorte && (
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
                      <Typography variant="subtitle2" color="primary.main">
                        Cohorte
                      </Typography>
                      <Typography variant="body1">
                        {user.cohorte.nombre} - {user.cohorte.descripcion}
                      </Typography>
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>

          {/* Información Personal */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <PersonIcon sx={{ mr: 1 }} />
                Información Personal
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Edad
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {user.edad ? `${user.edad} años` : 'No especificada'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Estado Civil
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {user.estado_civil || 'No especificado'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Lugar de Origen
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {user.lugar_origen || 'No especificado'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Residencia Actual
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {user.colonia_residencia_actual || 'No especificada'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Teléfono
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {user.celular || 'No especificado'}
                  </Typography>
                </Grid>
                
                {user.trabaja && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Lugar de Trabajo
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {user.lugar_trabajo || 'No especificado'}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Alertas y Notificaciones */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoIcon sx={{ mr: 1 }} />
                Información Importante
              </Typography>
              
              <List>
                {(!user.programas || user.programas.length === 0) && (
                  <ListItem>
                    <ListItemIcon>
                      <SchoolIcon color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Programa Educativo Pendiente"
                      secondary="Su programa educativo será asignado por el personal administrativo. Manténgase al pendiente."
                    />
                  </ListItem>
                )}
                
                {(!user.grupos || user.grupos.length === 0) && (
                  <>
                    <Divider />
                    <ListItem>
                      <ListItemIcon>
                        <GroupIcon color="warning" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Grupo Pendiente"
                        secondary="Su grupo será asignado por el personal administrativo. Manténgase al pendiente."
                      />
                    </ListItem>
                  </>
                )}
                
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <PsychologyIcon color="info" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Servicios de Apoyo Disponibles"
                    secondary="Recuerde que cuenta con servicios de apoyo psicopedagógico. Contacte al personal si necesita ayuda."
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

      </Grid>
    </Box>
  );
};

export default AlumnoDashboard;
