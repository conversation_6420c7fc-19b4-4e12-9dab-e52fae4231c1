from sqlalchemy import Column, Integer, String, Bo<PERSON>an, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base

class Persona(Base):
    __tablename__ = "personas"

    id = Column(Integer, primary_key=True, index=True)
    nombre = Column(String, nullable=False)
    apellido = Column(String, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    telefono = Column(String)
    direccion = Column(String)
    activo = Column(Boolean, default=True)
    fecha_creacion = Column(DateTime, server_default=func.now())
    fecha_actualizacion = Column(DateTime, onupdate=func.now())
    fecha_ultimo_contacto = Column(DateTime, server_default=func.now())
    fecha_sesion = Column(DateTime, server_default=func.now())
    cohorte_id = Column(Integer, ForeignKey("cohorte.id"), nullable=True)

    # Relaciones
    cohorte = relationship("Cohorte", back_populates="personas")
    grupos = relationship("Grupo", secondary="persona_grupo", back_populates="personas")
